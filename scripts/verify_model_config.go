package main

import (
	"fmt"
	"log"
	"strings"
	"solve_api/internal/config"
	"solve_api/internal/database"
	"solve_api/internal/model"
)

func main() {
	fmt.Println("🔍 验证模型配置表结构...")

	// 1. 加载配置
	cfg, err := config.LoadConfig("config/config.yaml")
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 2. 初始化数据库
	if err := database.InitMySQL(&cfg.Database.MySQL); err != nil {
		log.Fatalf("初始化数据库失败: %v", err)
	}
	defer database.CloseMySQL()

	db := database.GetDB()

	// 3. 验证表结构
	fmt.Println("📋 表结构信息:")
	rows, err := db.Raw("DESCRIBE model_configs").Rows()
	if err != nil {
		log.Fatalf("查询表结构失败: %v", err)
	}
	defer rows.Close()

	fmt.Printf("%-15s %-20s %-10s %-10s %-15s %-10s\n", "Field", "Type", "Null", "Key", "Default", "Extra")
	fmt.Println(strings.Repeat("-", 80))

	for rows.Next() {
		var field, fieldType, null, key, defaultVal, extra string
		if err := rows.Scan(&field, &fieldType, &null, &key, &defaultVal, &extra); err != nil {
			log.Printf("读取行失败: %v", err)
			continue
		}
		fmt.Printf("%-15s %-20s %-10s %-10s %-15s %-10s\n", field, fieldType, null, key, defaultVal, extra)
	}

	// 4. 验证数据
	fmt.Println("\n📊 数据验证:")
	var configs []model.ModelConfig
	if err := db.Find(&configs).Error; err != nil {
		log.Fatalf("查询数据失败: %v", err)
	}

	fmt.Printf("总记录数: %d\n", len(configs))
	fmt.Println("\n模型配置详情:")
	for _, config := range configs {
		fmt.Printf("  名称: %s\n", config.Name)
		fmt.Printf("  API地址: %s\n", config.ApiURL)
		fmt.Printf("  API密钥: %s\n", maskApiKey(config.ApiKey))
		fmt.Printf("  状态: %d (%s)\n", config.Status, getStatusName(config.Status))
		fmt.Printf("  参数: %s\n", config.Params)
		fmt.Printf("  创建时间: %s\n", config.CreatedAt.Format("2006-01-02 15:04:05"))
		fmt.Printf("  更新时间: %s\n", config.UpdatedAt.Format("2006-01-02 15:04:05"))
		fmt.Println("  " + strings.Repeat("-", 50))
	}

	// 5. 验证主键约束
	fmt.Println("🔑 主键约束验证:")
	
	// 尝试插入重复的主键（应该失败）
	duplicateConfig := &model.ModelConfig{
		Name:   "qwen-vl-plus", // 重复的主键
		ApiURL: "test",
		ApiKey: "test",
		Status: 1,
	}
	
	err = db.Create(duplicateConfig).Error
	if err != nil {
		fmt.Printf("✅ 主键约束正常工作: %v\n", err)
	} else {
		fmt.Printf("❌ 主键约束失效: 允许插入重复主键\n")
	}

	fmt.Println("\n🎉 验证完成！")
}

func maskApiKey(apiKey string) string {
	if len(apiKey) <= 8 {
		return apiKey
	}
	return apiKey[:4] + "****" + apiKey[len(apiKey)-4:]
}

func getStatusName(status int) string {
	switch status {
	case 1:
		return "启用"
	case 2:
		return "禁用"
	default:
		return "未知"
	}
}
