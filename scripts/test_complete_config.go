package main

import (
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"solve_api/internal/config"
	"solve_api/internal/database"
	"solve_api/internal/model"
	"gorm.io/gorm"
)

func main() {
	fmt.Println("🧪 测试完整模型配置...")

	// 1. 加载配置
	cfg, err := config.LoadConfig("config/config.yaml")
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 2. 初始化数据库
	if err := database.InitMySQL(&cfg.Database.MySQL); err != nil {
		log.Fatalf("初始化数据库失败: %v", err)
	}
	defer database.CloseMySQL()

	db := database.GetDB()

	// 3. 测试Qwen配置
	fmt.Println("🎯 测试Qwen-VL-Plus配置...")
	testQwenConfig(db)

	fmt.Println("\n" + strings.Repeat("=", 60) + "\n")

	// 4. 测试DeepSeek配置
	fmt.Println("🧠 测试DeepSeek-Chat配置...")
	testDeepSeekConfig(db)

	fmt.Println("\n🎉 所有配置测试完成！")
}

func testQwenConfig(db interface{}) {
	var qwenConfig model.ModelConfig
	if err := db.(*gorm.DB).Where("name = ?", "qwen-vl-plus").First(&qwenConfig).Error; err != nil {
		log.Fatalf("获取Qwen配置失败: %v", err)
	}

	params, err := qwenConfig.GetParamsMap()
	if err != nil {
		log.Fatalf("解析Qwen参数失败: %v", err)
	}

	fmt.Printf("📋 基本信息:\n")
	fmt.Printf("  模型名称: %s\n", qwenConfig.Name)
	fmt.Printf("  API地址: %s\n", qwenConfig.ApiURL)
	fmt.Printf("  状态: %s\n", getStatusName(qwenConfig.Status))

	fmt.Printf("\n🤖 模型配置:\n")
	fmt.Printf("  Model: %s\n", getString(params, "model"))

	fmt.Printf("\n💬 消息配置:\n")
	fmt.Printf("  System Message: %s\n", truncateString(getString(params, "system_message"), 80))
	fmt.Printf("  User Message: %s\n", getString(params, "user_message"))

	fmt.Printf("\n📄 输出格式:\n")
	if rf, ok := params["response_format"].(map[string]interface{}); ok {
		fmt.Printf("  Response Format: %s\n", rf["type"])
	}

	fmt.Printf("\n🔧 技术参数:\n")
	fmt.Printf("  Temperature: %.2f\n", getFloat(params, "temperature"))
	fmt.Printf("  Max Tokens: %d\n", getInt(params, "max_tokens"))
	fmt.Printf("  Top P: %.2f\n", getFloat(params, "top_p"))
	fmt.Printf("  Top K: %d\n", getInt(params, "top_k"))
	fmt.Printf("  Do Sample: %t\n", getBool(params, "do_sample"))
	fmt.Printf("  Frequency Penalty: %.1f\n", getFloat(params, "frequency_penalty"))
	fmt.Printf("  Presence Penalty: %.1f\n", getFloat(params, "presence_penalty"))
	fmt.Printf("  Detail: %s\n", getString(params, "detail"))

	// 构建完整请求体
	qwenRequest := buildQwenRequest(params)
	fmt.Printf("\n📤 完整请求体示例:\n")
	requestJSON, _ := json.MarshalIndent(qwenRequest, "", "  ")
	fmt.Println(string(requestJSON))
}

func testDeepSeekConfig(db interface{}) {
	var deepseekConfig model.ModelConfig
	if err := db.(*gorm.DB).Where("name = ?", "deepseek-chat").First(&deepseekConfig).Error; err != nil {
		log.Fatalf("获取DeepSeek配置失败: %v", err)
	}

	params, err := deepseekConfig.GetParamsMap()
	if err != nil {
		log.Fatalf("解析DeepSeek参数失败: %v", err)
	}

	fmt.Printf("📋 基本信息:\n")
	fmt.Printf("  模型名称: %s\n", deepseekConfig.Name)
	fmt.Printf("  API地址: %s\n", deepseekConfig.ApiURL)
	fmt.Printf("  状态: %s\n", getStatusName(deepseekConfig.Status))

	fmt.Printf("\n🤖 模型配置:\n")
	fmt.Printf("  Model: %s\n", getString(params, "model"))

	fmt.Printf("\n💬 消息配置:\n")
	fmt.Printf("  System Message: %s\n", truncateString(getString(params, "system_message"), 80))
	fmt.Printf("  User Message: %s\n", getString(params, "user_message"))

	fmt.Printf("\n📄 输出格式:\n")
	if rf, ok := params["response_format"].(map[string]interface{}); ok {
		fmt.Printf("  Response Format: %s\n", rf["type"])
	}

	fmt.Printf("\n🔧 技术参数:\n")
	fmt.Printf("  Temperature: %.2f\n", getFloat(params, "temperature"))
	fmt.Printf("  Max Tokens: %d\n", getInt(params, "max_tokens"))
	fmt.Printf("  Top P: %.2f\n", getFloat(params, "top_p"))

	// 构建完整请求体
	deepseekRequest := buildDeepSeekRequest(params)
	fmt.Printf("\n📤 完整请求体示例:\n")
	requestJSON, _ := json.MarshalIndent(deepseekRequest, "", "  ")
	fmt.Println(string(requestJSON))
}

func buildQwenRequest(params map[string]interface{}) map[string]interface{} {
	return map[string]interface{}{
		"model": getString(params, "model"),
		"input": map[string]interface{}{
			"messages": []map[string]interface{}{
				{
					"role":    "system",
					"content": getString(params, "system_message"),
				},
				{
					"role": "user",
					"content": []map[string]interface{}{
						{"image": "http://solve.igmdns.com/img/23.jpg"},
						{"text": getString(params, "user_message")},
					},
				},
			},
		},
		"parameters": map[string]interface{}{
			"temperature":        getFloat(params, "temperature"),
			"max_tokens":         getInt(params, "max_tokens"),
			"top_p":              getFloat(params, "top_p"),
			"top_k":              getInt(params, "top_k"),
			"do_sample":          getBool(params, "do_sample"),
			"frequency_penalty":  getFloat(params, "frequency_penalty"),
			"presence_penalty":   getFloat(params, "presence_penalty"),
			"detail":             getString(params, "detail"),
			"response_format":    params["response_format"],
		},
	}
}

func buildDeepSeekRequest(params map[string]interface{}) map[string]interface{} {
	return map[string]interface{}{
		"model": getString(params, "model"),
		"messages": []map[string]interface{}{
			{
				"role":    "system",
				"content": getString(params, "system_message"),
			},
			{
				"role":    "user",
				"content": getString(params, "user_message") + "\n\n题目信息: {从Qwen识别的结果}",
			},
		},
		"temperature":     getFloat(params, "temperature"),
		"max_tokens":      getInt(params, "max_tokens"),
		"top_p":           getFloat(params, "top_p"),
		"response_format": params["response_format"],
	}
}

// 辅助函数
func getStatusName(status int) string {
	switch status {
	case 1:
		return "启用"
	case 2:
		return "禁用"
	default:
		return "未知"
	}
}

func getFloat(params map[string]interface{}, key string) float64 {
	if val, ok := params[key]; ok {
		if f, ok := val.(float64); ok {
			return f
		}
	}
	return 0
}

func getInt(params map[string]interface{}, key string) int {
	if val, ok := params[key]; ok {
		if f, ok := val.(float64); ok {
			return int(f)
		}
	}
	return 0
}

func getBool(params map[string]interface{}, key string) bool {
	if val, ok := params[key]; ok {
		if b, ok := val.(bool); ok {
			return b
		}
	}
	return false
}

func getString(params map[string]interface{}, key string) string {
	if val, ok := params[key]; ok {
		if s, ok := val.(string); ok {
			return s
		}
	}
	return ""
}

func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen] + "..."
}
