<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拍照搜题 - API测试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 20px 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .content {
            padding: 30px;
        }

        .test-form {
            background: #f9fafb;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #4f46e5;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
        }

        .form-group input, .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group textarea:focus {
            outline: none;
            border-color: #4f46e5;
        }

        .btn {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
            margin-right: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .results-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 30px;
        }

        .result-panel {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
        }

        .result-header {
            padding: 15px 20px;
            font-weight: 600;
            color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .qwen-header {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        .deepseek-header {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .result-content {
            padding: 20px;
            max-height: 500px;
            overflow-y: auto;
        }

        .result-json {
            background: #1f2937;
            color: #e5e7eb;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            line-height: 1.5;
            white-space: pre-wrap;
            word-break: break-all;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 3px solid #f3f4f6;
            border-top: 3px solid #4f46e5;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-error {
            background: #fee2e2;
            color: #dc2626;
        }

        .status-loading {
            background: #dbeafe;
            color: #1d4ed8;
        }

        .image-preview {
            max-width: 100%;
            max-height: 300px;
            border-radius: 8px;
            margin-top: 10px;
            border: 2px solid #e5e7eb;
        }

        .hidden {
            display: none !important;
        }

        .error-message {
            background: #fee2e2;
            color: #dc2626;
            padding: 12px 16px;
            border-radius: 8px;
            margin-top: 10px;
            border: 1px solid #fecaca;
        }

        .success-message {
            background: #dcfce7;
            color: #166534;
            padding: 12px 16px;
            border-radius: 8px;
            margin-top: 10px;
            border: 1px solid #bbf7d0;
        }

        .timing-info {
            font-size: 11px;
            opacity: 0.8;
        }

        .response-stats {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            font-size: 12px;
        }

        .stat-item {
            background: #f3f4f6;
            padding: 6px 12px;
            border-radius: 6px;
        }

        @media (max-width: 768px) {
            .results-container {
                grid-template-columns: 1fr;
            }
            
            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 拍照搜题 API测试</h1>
            <p>测试图片识别和题目解答的完整流程</p>
        </div>

        <div class="content">
            <!-- 测试表单 -->
            <div class="test-form">
                <h3 style="margin-bottom: 20px; color: #4f46e5;">📸 图片搜题测试</h3>
                
                <div class="form-group">
                    <label for="imageUrl">图片URL</label>
                    <input type="url" id="imageUrl" placeholder="请输入图片URL，例如：http://solve.igmdns.com/img/23.jpg" 
                           value="http://solve.igmdns.com/img/23.jpg">
                </div>

                <div class="form-group">
                    <label for="apiKey">API密钥 (可选)</label>
                    <input type="password" id="apiKey" placeholder="如果需要认证，请输入API密钥">
                </div>

                <div style="display: flex; align-items: center; gap: 15px;">
                    <button class="btn" onclick="testSearch()" id="testBtn">
                        🚀 开始搜题测试
                    </button>
                    <button class="btn" onclick="clearResults()" style="background: #6b7280;">
                        🗑️ 清空结果
                    </button>
                    <button class="btn" onclick="previewImage()" style="background: #059669;">
                        👁️ 预览图片
                    </button>
                </div>

                <div id="imagePreview" class="hidden">
                    <img id="previewImg" class="image-preview" alt="图片预览">
                </div>

                <div id="errorMessage"></div>
            </div>

            <!-- 加载状态 -->
            <div id="loading" class="loading">
                <div class="spinner"></div>
                <p>正在处理图片，请稍候...</p>
            </div>

            <!-- 结果展示 -->
            <div class="results-container">
                <!-- Qwen结果 -->
                <div class="result-panel">
                    <div class="result-header qwen-header">
                        <div>
                            <span>🎯 Qwen-VL-Plus (图像识别)</span>
                            <div class="timing-info" id="qwenTiming"></div>
                        </div>
                        <span class="status-badge" id="qwenStatus">待测试</span>
                    </div>
                    <div class="result-content">
                        <div class="response-stats" id="qwenStats" style="display: none;">
                            <div class="stat-item">
                                <strong>响应时间:</strong> <span id="qwenTime">-</span>
                            </div>
                            <div class="stat-item">
                                <strong>状态码:</strong> <span id="qwenCode">-</span>
                            </div>
                        </div>
                        <div id="qwenResult">
                            <p style="color: #6b7280; text-align: center; padding: 40px;">
                                点击"开始搜题测试"查看Qwen图像识别结果
                            </p>
                        </div>
                    </div>
                </div>

                <!-- DeepSeek结果 -->
                <div class="result-panel">
                    <div class="result-header deepseek-header">
                        <div>
                            <span>🧠 DeepSeek-Chat (题目解答)</span>
                            <div class="timing-info" id="deepseekTiming"></div>
                        </div>
                        <span class="status-badge" id="deepseekStatus">待测试</span>
                    </div>
                    <div class="result-content">
                        <div class="response-stats" id="deepseekStats" style="display: none;">
                            <div class="stat-item">
                                <strong>响应时间:</strong> <span id="deepseekTime">-</span>
                            </div>
                            <div class="stat-item">
                                <strong>状态码:</strong> <span id="deepseekCode">-</span>
                            </div>
                        </div>
                        <div id="deepseekResult">
                            <p style="color: #6b7280; text-align: center; padding: 40px;">
                                等待Qwen识别完成后自动开始DeepSeek解答
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api/v1';
        let testResults = {
            qwen: null,
            deepseek: null
        };

        // 预览图片
        function previewImage() {
            const imageUrl = document.getElementById('imageUrl').value;
            if (!imageUrl) {
                showError('请先输入图片URL');
                return;
            }

            const previewDiv = document.getElementById('imagePreview');
            const previewImg = document.getElementById('previewImg');
            
            previewImg.src = imageUrl;
            previewImg.onload = function() {
                previewDiv.classList.remove('hidden');
            };
            previewImg.onerror = function() {
                showError('图片加载失败，请检查URL是否正确');
            };
        }

        // 开始搜题测试
        async function testSearch() {
            const imageUrl = document.getElementById('imageUrl').value;
            const apiKey = document.getElementById('apiKey').value;

            if (!imageUrl) {
                showError('请输入图片URL');
                return;
            }

            // 重置状态
            clearResults();
            showLoading(true);
            setTestButtonState(true);

            try {
                // 第一步：Qwen图像识别
                await testQwenRecognition(imageUrl, apiKey);
                
                // 第二步：DeepSeek题目解答
                if (testResults.qwen && testResults.qwen.success) {
                    await testDeepSeekSolving(testResults.qwen.data, apiKey);
                }
            } catch (error) {
                showError('测试过程中发生错误: ' + error.message);
            } finally {
                showLoading(false);
                setTestButtonState(false);
            }
        }

        // 测试Qwen图像识别
        async function testQwenRecognition(imageUrl, apiKey) {
            updateStatus('qwen', 'loading', '识别中...');
            const startTime = Date.now();

            try {
                const headers = {
                    'Content-Type': 'application/json'
                };
                
                if (apiKey) {
                    headers['Authorization'] = `Bearer ${apiKey}`;
                }

                const response = await fetch(`${API_BASE}/api/search`, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify({
                        image_url: imageUrl
                    })
                });

                const endTime = Date.now();
                const responseTime = endTime - startTime;

                const data = await response.json();

                // 更新统计信息
                updateStats('qwen', responseTime, response.status);

                if (response.ok && data.code === 200) {
                    testResults.qwen = {
                        success: true,
                        data: data.data,
                        responseTime: responseTime
                    };
                    
                    updateStatus('qwen', 'success', '识别成功');
                    displayResult('qwen', data);
                } else {
                    testResults.qwen = {
                        success: false,
                        error: data.message || '识别失败'
                    };
                    
                    updateStatus('qwen', 'error', '识别失败');
                    displayError('qwen', data.message || '识别失败');
                }
            } catch (error) {
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                testResults.qwen = {
                    success: false,
                    error: error.message
                };
                
                updateStatus('qwen', 'error', '网络错误');
                updateStats('qwen', responseTime, 'ERROR');
                displayError('qwen', '网络错误: ' + error.message);
            }
        }

        // 测试DeepSeek题目解答
        async function testDeepSeekSolving(qwenData, apiKey) {
            updateStatus('deepseek', 'loading', '解答中...');
            const startTime = Date.now();

            try {
                const headers = {
                    'Content-Type': 'application/json'
                };
                
                if (apiKey) {
                    headers['Authorization'] = `Bearer ${apiKey}`;
                }

                // 构建DeepSeek请求，使用Qwen的识别结果
                const requestBody = {
                    question_info: qwenData.question_info || qwenData,
                    image_url: document.getElementById('imageUrl').value
                };

                const response = await fetch(`${API_BASE}/api/search`, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(requestBody)
                });

                const endTime = Date.now();
                const responseTime = endTime - startTime;

                const data = await response.json();

                // 更新统计信息
                updateStats('deepseek', responseTime, response.status);

                if (response.ok && data.code === 200) {
                    testResults.deepseek = {
                        success: true,
                        data: data.data,
                        responseTime: responseTime
                    };
                    
                    updateStatus('deepseek', 'success', '解答成功');
                    displayResult('deepseek', data);
                } else {
                    testResults.deepseek = {
                        success: false,
                        error: data.message || '解答失败'
                    };
                    
                    updateStatus('deepseek', 'error', '解答失败');
                    displayError('deepseek', data.message || '解答失败');
                }
            } catch (error) {
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                testResults.deepseek = {
                    success: false,
                    error: error.message
                };
                
                updateStatus('deepseek', 'error', '网络错误');
                updateStats('deepseek', responseTime, 'ERROR');
                displayError('deepseek', '网络错误: ' + error.message);
            }
        }

        // 更新状态
        function updateStatus(model, status, text) {
            const statusElement = document.getElementById(`${model}Status`);
            statusElement.textContent = text;
            statusElement.className = `status-badge status-${status}`;
        }

        // 更新统计信息
        function updateStats(model, responseTime, statusCode) {
            document.getElementById(`${model}Stats`).style.display = 'flex';
            document.getElementById(`${model}Time`).textContent = responseTime + 'ms';
            document.getElementById(`${model}Code`).textContent = statusCode;
        }

        // 显示结果
        function displayResult(model, data) {
            const resultElement = document.getElementById(`${model}Result`);
            resultElement.innerHTML = `<div class="result-json">${JSON.stringify(data, null, 2)}</div>`;
        }

        // 显示错误
        function displayError(model, error) {
            const resultElement = document.getElementById(`${model}Result`);
            resultElement.innerHTML = `<div class="error-message">❌ ${error}</div>`;
        }

        // 显示/隐藏加载状态
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        // 设置测试按钮状态
        function setTestButtonState(disabled) {
            const btn = document.getElementById('testBtn');
            btn.disabled = disabled;
            btn.textContent = disabled ? '🔄 测试中...' : '🚀 开始搜题测试';
        }

        // 清空结果
        function clearResults() {
            testResults = { qwen: null, deepseek: null };
            
            // 重置状态
            updateStatus('qwen', '', '待测试');
            updateStatus('deepseek', '', '待测试');
            
            // 隐藏统计信息
            document.getElementById('qwenStats').style.display = 'none';
            document.getElementById('deepseekStats').style.display = 'none';
            
            // 清空结果
            document.getElementById('qwenResult').innerHTML = 
                '<p style="color: #6b7280; text-align: center; padding: 40px;">点击"开始搜题测试"查看Qwen图像识别结果</p>';
            document.getElementById('deepseekResult').innerHTML = 
                '<p style="color: #6b7280; text-align: center; padding: 40px;">等待Qwen识别完成后自动开始DeepSeek解答</p>';
            
            // 清空错误信息
            document.getElementById('errorMessage').innerHTML = '';
        }

        // 显示错误信息
        function showError(message) {
            document.getElementById('errorMessage').innerHTML = 
                `<div class="error-message">❌ ${message}</div>`;
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 支持回车键提交
            document.getElementById('imageUrl').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    testSearch();
                }
            });
        });
    </script>
</body>
</html>
