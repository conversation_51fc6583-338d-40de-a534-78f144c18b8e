package main

import (
	"fmt"
	"log"
	"solve_api/internal/config"
	"solve_api/internal/database"
	"solve_api/internal/model"
)

func main() {
	fmt.Println("🚀 开始模型配置表结构迁移...")

	// 1. 加载配置
	cfg, err := config.LoadConfig("config/config.yaml")
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 2. 初始化数据库
	if err := database.InitMySQL(&cfg.Database.MySQL); err != nil {
		log.Fatalf("初始化数据库失败: %v", err)
	}
	defer database.CloseMySQL()

	db := database.GetDB()

	// 3. 备份现有数据
	fmt.Println("📋 备份现有模型配置数据...")
	var existingConfigs []map[string]interface{}
	
	// 检查表是否存在
	var tableExists int64
	err = db.Raw("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'model_configs'").Scan(&tableExists).Error
	if err != nil {
		log.Fatalf("检查表存在性失败: %v", err)
	}

	if tableExists > 0 {
		// 备份现有数据
		rows, err := db.Raw("SELECT name, api_url, api_key, params, status FROM model_configs").Rows()
		if err != nil {
			log.Printf("⚠️ 备份数据失败: %v", err)
		} else {
			defer rows.Close()
			for rows.Next() {
				var name, apiURL, apiKey, params string
				var status int
				if err := rows.Scan(&name, &apiURL, &apiKey, &params, &status); err != nil {
					log.Printf("⚠️ 读取数据失败: %v", err)
					continue
				}
				existingConfigs = append(existingConfigs, map[string]interface{}{
					"name":    name,
					"api_url": apiURL,
					"api_key": apiKey,
					"params":  params,
					"status":  status,
				})
			}
			fmt.Printf("✅ 备份了 %d 条现有配置\n", len(existingConfigs))
		}
	}

	// 4. 删除现有表并重新创建
	fmt.Println("🔄 重新创建模型配置表...")
	if err := db.Migrator().DropTable(&model.ModelConfig{}); err != nil {
		log.Printf("⚠️ 删除旧表失败: %v", err)
	}

	// 5. 创建新表结构
	if err := db.AutoMigrate(&model.ModelConfig{}); err != nil {
		log.Fatalf("创建新表结构失败: %v", err)
	}
	fmt.Println("✅ 新表结构创建成功")

	// 6. 初始化固定的两个模型配置
	fmt.Println("📝 初始化固定模型配置...")
	
	// Qwen模型配置
	qwenConfig := &model.ModelConfig{
		Name:   model.ModelNameQwenVLPlus,
		ApiURL: "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation",
		ApiKey: "sk-placeholder", // 需要管理员后续配置
		Status: model.ModelConfigStatusEnabled,
	}
	
	// 设置Qwen默认参数
	qwenParams := model.GetDefaultModelParams(model.ModelNameQwenVLPlus)
	if err := qwenConfig.SetParamsMap(qwenParams); err != nil {
		log.Fatalf("设置Qwen参数失败: %v", err)
	}

	// DeepSeek模型配置
	deepseekConfig := &model.ModelConfig{
		Name:   model.ModelNameDeepseekChat,
		ApiURL: "https://api.deepseek.com/v1/chat/completions",
		ApiKey: "sk-placeholder", // 需要管理员后续配置
		Status: model.ModelConfigStatusEnabled,
	}
	
	// 设置DeepSeek默认参数
	deepseekParams := model.GetDefaultModelParams(model.ModelNameDeepseekChat)
	if err := deepseekConfig.SetParamsMap(deepseekParams); err != nil {
		log.Fatalf("设置DeepSeek参数失败: %v", err)
	}

	// 7. 尝试恢复API密钥（如果备份中有的话）
	for _, config := range existingConfigs {
		name := config["name"].(string)
		apiKey := config["api_key"].(string)
		
		if name == model.ModelNameQwenVLPlus && apiKey != "sk-placeholder" && apiKey != "" {
			qwenConfig.ApiKey = apiKey
			fmt.Printf("🔑 恢复Qwen API密钥\n")
		}
		if name == model.ModelNameDeepseekChat && apiKey != "sk-placeholder" && apiKey != "" {
			deepseekConfig.ApiKey = apiKey
			fmt.Printf("🔑 恢复DeepSeek API密钥\n")
		}
	}

	// 8. 保存配置到数据库
	if err := db.Create(qwenConfig).Error; err != nil {
		log.Fatalf("创建Qwen配置失败: %v", err)
	}
	fmt.Printf("✅ Qwen模型配置创建成功\n")

	if err := db.Create(deepseekConfig).Error; err != nil {
		log.Fatalf("创建DeepSeek配置失败: %v", err)
	}
	fmt.Printf("✅ DeepSeek模型配置创建成功\n")

	// 9. 验证结果
	fmt.Println("🔍 验证迁移结果...")
	var count int64
	if err := db.Model(&model.ModelConfig{}).Count(&count).Error; err != nil {
		log.Fatalf("验证失败: %v", err)
	}

	if count != 2 {
		log.Fatalf("❌ 迁移失败：期望2个模型，实际%d个", count)
	}

	fmt.Println("🎉 模型配置表结构迁移完成！")
	fmt.Println("")
	fmt.Println("📋 迁移总结:")
	fmt.Println("  - 使用模型名称作为主键")
	fmt.Println("  - 移除了自增ID字段")
	fmt.Println("  - 固定支持两个模型：qwen-vl-plus, deepseek-chat")
	fmt.Println("  - 保留了API密钥（如果之前已配置）")
	fmt.Println("")
	fmt.Println("⚠️ 注意事项:")
	fmt.Println("  - 请确保API密钥已正确配置")
	fmt.Println("  - 基于ID的API接口已移除，请使用基于名称的接口")
	fmt.Println("  - 模型配置不再支持删除操作")
}
