package model

import (
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

// ModelConfig 模型配置表
// 使用模型名称作为主键，只支持固定的两个模型：qwen-vl-plus 和 deepseek-chat
type ModelConfig struct {
	Name      string    `gorm:"primaryKey;size:50" json:"name"`                    // 模型名称作为主键
	ApiURL    string    `gorm:"size:255;not null" json:"api_url"`                  // API地址
	ApiKey    string    `gorm:"size:100;not null" json:"api_key"`                  // API密钥
	Params    string    `gorm:"type:text;comment:'JSON格式的参数配置'" json:"params"`    // 参数配置
	Status    int       `gorm:"default:1;comment:'1:启用 2:禁用'" json:"status"`      // 状态（固定为启用）
	CreatedAt time.Time `json:"created_at"`                                        // 创建时间
	UpdatedAt time.Time `json:"updated_at"`                                        // 更新时间
}

// TableName 指定表名
func (ModelConfig) TableName() string {
	return "model_configs"
}

// ModelConfigStatus 模型配置状态常量
const (
	ModelConfigStatusEnabled  = 1 // 启用
	ModelConfigStatusDisabled = 2 // 禁用
)

// IsEnabled 检查模型是否启用
func (m *ModelConfig) IsEnabled() bool {
	return m.Status == ModelConfigStatusEnabled
}

// IsDisabled 检查模型是否禁用
func (m *ModelConfig) IsDisabled() bool {
	return m.Status == ModelConfigStatusDisabled
}

// GetParamsMap 获取参数映射
func (m *ModelConfig) GetParamsMap() (map[string]interface{}, error) {
	if m.Params == "" {
		return make(map[string]interface{}), nil
	}

	var params map[string]interface{}
	err := json.Unmarshal([]byte(m.Params), &params)
	if err != nil {
		return nil, err
	}

	return params, nil
}

// SetParamsMap 设置参数映射
func (m *ModelConfig) SetParamsMap(params map[string]interface{}) error {
	data, err := json.Marshal(params)
	if err != nil {
		return err
	}

	m.Params = string(data)
	return nil
}

// ModelConfigCreateRequest 创建模型配置请求
type ModelConfigCreateRequest struct {
	Name   string                 `json:"name" binding:"required,min=1,max=50" example:"qwen-vl-plus"`
	ApiURL string                 `json:"api_url" binding:"required,url" example:"https://api.example.com/v1/chat"`
	ApiKey string                 `json:"api_key" binding:"required" example:"sk-xxx"`
	Params map[string]interface{} `json:"params" example:"{\"temperature\":0.7,\"max_tokens\":1000}"`
	Status int                    `json:"status" binding:"omitempty,oneof=1 2" example:"1"`
}

// ModelConfigUpdateRequest 更新模型配置请求
type ModelConfigUpdateRequest struct {
	Name   string                 `json:"name" binding:"omitempty,min=1,max=50" example:"qwen-vl-plus"`
	ApiURL string                 `json:"api_url" binding:"omitempty,url" example:"https://api.example.com/v1/chat"`
	ApiKey string                 `json:"api_key" binding:"omitempty" example:"sk-xxx"`
	Params map[string]interface{} `json:"params" example:"{\"temperature\":0.7,\"max_tokens\":1000}"`
	Status int                    `json:"status" binding:"omitempty,oneof=1 2" example:"1"`
}

// ModelConfigStatusUpdateRequest 更新模型状态请求
type ModelConfigStatusUpdateRequest struct {
	Status int `json:"status" binding:"required,oneof=1 2" example:"1"`
}

// ModelConfigParamsUpdateRequest 更新模型参数请求
type ModelConfigParamsUpdateRequest struct {
	Params map[string]interface{} `json:"params" binding:"required" example:"{\"temperature\":0.7,\"max_tokens\":1000}"`
}

// ModelConfigApiKeyUpdateRequest 更新模型API密钥请求
type ModelConfigApiKeyUpdateRequest struct {
	ApiKey string `json:"api_key" binding:"required" example:"sk-xxx"`
}

// FixedModelsResponse 固定模型配置响应
type FixedModelsResponse struct {
	Qwen     *ModelConfigResponse `json:"qwen"`
	DeepSeek *ModelConfigResponse `json:"deepseek"`
}

// ModelConfigResponse 模型配置响应
type ModelConfigResponse struct {
	Name       string                 `json:"name"`              // 模型名称（主键）
	ApiURL     string                 `json:"api_url"`           // API地址
	ApiKey     string                 `json:"api_key,omitempty"` // API密钥（可选择是否返回）
	Params     map[string]interface{} `json:"params"`            // 参数配置
	Status     int                    `json:"status"`            // 状态
	StatusName string                 `json:"status_name"`       // 状态名称
	CreatedAt  time.Time              `json:"created_at"`        // 创建时间
	UpdatedAt  time.Time              `json:"updated_at"`        // 更新时间
}

// ToResponse 转换为响应格式
func (m *ModelConfig) ToResponse(includeApiKey bool) *ModelConfigResponse {
	params, _ := m.GetParamsMap()

	statusName := ""
	switch m.Status {
	case ModelConfigStatusEnabled:
		statusName = "启用"
	case ModelConfigStatusDisabled:
		statusName = "禁用"
	}

	response := &ModelConfigResponse{
		Name:       m.Name,
		ApiURL:     m.ApiURL,
		Params:     params,
		Status:     m.Status,
		StatusName: statusName,
		CreatedAt:  m.CreatedAt,
		UpdatedAt:  m.UpdatedAt,
	}

	if includeApiKey {
		response.ApiKey = m.ApiKey
	}

	return response
}

// ModelConfigListResponse 模型配置列表响应（不包含ApiKey）
type ModelConfigListResponse struct {
	Name       string                 `json:"name"`         // 模型名称（主键）
	ApiURL     string                 `json:"api_url"`      // API地址
	Params     map[string]interface{} `json:"params"`       // 参数配置
	Status     int                    `json:"status"`       // 状态
	StatusName string                 `json:"status_name"`  // 状态名称
	CreatedAt  time.Time              `json:"created_at"`   // 创建时间
	UpdatedAt  time.Time              `json:"updated_at"`   // 更新时间
}

// ToListResponse 转换为列表响应格式（不包含ApiKey）
func (m *ModelConfig) ToListResponse() *ModelConfigListResponse {
	params, _ := m.GetParamsMap()

	statusName := ""
	switch m.Status {
	case ModelConfigStatusEnabled:
		statusName = "启用"
	case ModelConfigStatusDisabled:
		statusName = "禁用"
	}

	return &ModelConfigListResponse{
		Name:       m.Name,
		ApiURL:     m.ApiURL,
		Params:     params,
		Status:     m.Status,
		StatusName: statusName,
		CreatedAt:  m.CreatedAt,
		UpdatedAt:  m.UpdatedAt,
	}
}

// 预定义的模型名称常量
const (
	ModelNameQwenVLPlus   = "qwen-vl-plus"
	ModelNameDeepseekChat = "deepseek-chat"
)

// GetDefaultModelParams 获取默认模型参数
func GetDefaultModelParams(modelName string) map[string]interface{} {
	switch modelName {
	case ModelNameQwenVLPlus:
		return map[string]interface{}{
			"temperature":        0.3,  // 降低随机性，提高识别准确性
			"max_tokens":         1500, // 增加token数量以支持更详细的结构化输出
			"top_p":              0.8,  // 保持适度的多样性
			"response_format":    map[string]string{"type": "json_object"}, // 强制JSON输出
			"detail":             "high", // 高精度图像分析
		}
	case ModelNameDeepseekChat:
		return map[string]interface{}{
			"temperature":     0.3,  // 降低随机性，提高解析准确性
			"max_tokens":      2500, // 增加token数量以支持详细解析
			"top_p":           0.8,  // 保持适度的多样性
			"response_format": map[string]string{"type": "json_object"}, // 强制JSON输出
		}
	default:
		return map[string]interface{}{
			"temperature": 0.3,
			"max_tokens":  1500,
			"top_p":       0.8,
		}
	}
}
